{"name": "sentra-auto-browser", "version": "1.0.0", "description": "智能浏览器自动化工具 - 基于AI的网页操作助手", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"sentra-auto": "dist/cli/index.js"}, "scripts": {"build": "tsc", "dev": "ts-node src/cli/index.ts", "start": "node dist/cli/index.js", "test": "jest", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts", "demo:simple": "ts-node examples/simple.ts", "demo:custom": "ts-node examples/custom-config.ts", "demo:multiple": "ts-node examples/multiple-tasks.ts", "demo:advanced": "ts-node examples/advanced-features.ts", "demo:gemini": "ts-node examples/google-gemini.ts", "install-browser": "npx playwright install chromium", "clean": "rm -rf dist", "prebuild": "npm run clean", "validate": "npm run lint && npm run format && npm run test"}, "keywords": ["浏览器自动化", "智能代理", "AI助手", "网页操作", "playwright", "llm", "automation", "browser", "ai"], "author": "Sentra Auto Browser Team", "license": "MIT", "dependencies": {"@anthropic-ai/sdk": "^0.54.0", "@google/generative-ai": "^0.21.0", "ansi-regex": "^6.1.0", "chalk": "^5.4.1", "cli-cursor": "^5.0.0", "cli-spinners": "^3.2.0", "commander": "^12.1.0", "dotenv": "^16.4.7", "fs-extra": "^11.2.0", "is-interactive": "^2.0.0", "is-unicode-supported": "^2.1.0", "lodash": "^4.17.21", "log-symbols": "^7.0.1", "openai": "^4.81.0", "ora": "^8.1.1", "path": "^0.12.7", "playwright": "^1.52.0", "stdin-discarder": "^0.2.2", "string-width": "^7.2.0", "strip-ansi": "^7.1.0", "uuid": "^10.0.0", "wcwidth": "^1.0.1"}, "devDependencies": {"@types/node": "^22.10.2", "typescript": "^5.7.2", "ts-node": "^10.9.2", "@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "eslint": "^9.17.0", "prettier": "^3.4.2", "jest": "^29.7.0", "@types/jest": "^29.5.14", "@types/uuid": "^10.0.0", "@types/lodash": "^4.17.13", "@types/fs-extra": "^11.0.4", "ts-jest": "^29.2.5"}, "engines": {"node": ">=18.0.0"}}